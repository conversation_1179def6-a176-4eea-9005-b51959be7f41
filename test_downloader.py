#!/usr/bin/env python3
"""
Test script for YouTube Transcript Downloader

This script tests the basic functionality of the downloader with a small sample.
"""

import os
import tempfile
import shutil
from youtube_transcript_downloader import YouTubeTranscriptDownloader


def test_basic_functionality():
    """Test basic functionality with a known video."""
    print("Testing YouTube Transcript Downloader...")
    
    # Create temporary directory for testing
    test_dir = tempfile.mkdtemp(prefix="yt_transcript_test_")
    print(f"Test directory: {test_dir}")
    
    try:
        # Initialize downloader
        downloader = YouTubeTranscriptDownloader(output_dir=test_dir, log_level="INFO")
        
        # Test with a known educational channel (3Blue1Brown)
        # This channel typically has transcripts available
        channel_url = "https://www.youtube.com/@3blue1brown"
        
        print(f"\n1. Testing channel video retrieval...")
        videos = downloader.get_channel_videos(channel_url, max_videos=5)
        
        if videos:
            print(f"✅ Successfully retrieved {len(videos)} videos")
            print(f"   Sample video: {videos[0]['title']}")
        else:
            print("❌ Failed to retrieve videos")
            return False
        
        print(f"\n2. Testing transcript download...")
        criteria = {'latest': 2}  # Just test with 2 latest videos
        
        results = downloader.download_transcripts(
            channel_url=channel_url,
            criteria=criteria,
            output_format='txt',
            include_timestamps=False
        )
        
        if results['success'] and results['downloaded'] > 0:
            print(f"✅ Successfully downloaded {results['downloaded']} transcripts")
            print(f"   Files created: {len(results['files'])}")
            
            # Check if files were actually created
            for file_path in results['files']:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   📄 {os.path.basename(file_path)} ({file_size} bytes)")
                else:
                    print(f"   ❌ File not found: {file_path}")
        else:
            print(f"❌ Download failed or no transcripts available")
            print(f"   Error: {results.get('error', 'Unknown error')}")
            if results.get('errors'):
                for error in results['errors'][:3]:
                    print(f"   - {error}")
        
        print(f"\n3. Testing different output formats...")
        
        # Test JSON format
        if videos:
            video_id = videos[0]['id']
            transcript_data = downloader.get_transcript(video_id)
            
            if transcript_data:
                # Test saving in different formats
                formats_tested = []
                
                for fmt in ['txt', 'json', 'csv']:
                    try:
                        file_path = downloader.save_transcript(
                            videos[0], transcript_data, fmt, include_timestamps=True
                        )
                        if os.path.exists(file_path):
                            formats_tested.append(fmt)
                            print(f"   ✅ {fmt.upper()} format: {os.path.basename(file_path)}")
                        else:
                            print(f"   ❌ {fmt.upper()} format: File not created")
                    except Exception as e:
                        print(f"   ❌ {fmt.upper()} format: Error - {str(e)}")
                
                if len(formats_tested) >= 2:
                    print(f"✅ Multiple formats working: {', '.join(formats_tested)}")
                else:
                    print(f"⚠️  Limited format support: {', '.join(formats_tested)}")
            else:
                print("⚠️  No transcript available for format testing")
        
        print(f"\n4. Testing filtering functionality...")
        
        # Test filtering
        if len(videos) > 1:
            filter_criteria = {
                'min_duration': 60,  # At least 1 minute
                'latest': 3
            }
            
            filtered_videos = downloader.filter_videos_by_criteria(videos, filter_criteria)
            print(f"   Original videos: {len(videos)}")
            print(f"   After filtering: {len(filtered_videos)}")
            
            if len(filtered_videos) < len(videos):
                print("   ✅ Filtering is working")
            else:
                print("   ⚠️  Filtering may not be working as expected")
        
        print(f"\n📊 Test Summary:")
        print(f"   - Video retrieval: {'✅' if videos else '❌'}")
        print(f"   - Transcript download: {'✅' if results.get('downloaded', 0) > 0 else '❌'}")
        print(f"   - File creation: {'✅' if results.get('files') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test directory
        try:
            shutil.rmtree(test_dir)
            print(f"\n🧹 Cleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"⚠️  Could not clean up test directory: {str(e)}")


def test_cli_help():
    """Test that the CLI help works."""
    print("\n" + "="*50)
    print("Testing CLI Help...")
    
    try:
        import subprocess
        result = subprocess.run(
            ["python", "youtube_transcript_downloader.py", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and "Download YouTube transcripts" in result.stdout:
            print("✅ CLI help is working")
            return True
        else:
            print("❌ CLI help failed")
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ CLI test failed: {str(e)}")
        return False


if __name__ == '__main__':
    print("YouTube Transcript Downloader - Test Suite")
    print("=" * 50)
    
    # Run basic functionality test
    basic_test_passed = test_basic_functionality()
    
    # Run CLI test
    cli_test_passed = test_cli_help()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print(f"Basic functionality: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"CLI interface: {'✅ PASSED' if cli_test_passed else '❌ FAILED'}")
    
    if basic_test_passed and cli_test_passed:
        print("\n🎉 All tests passed! The downloader is ready to use.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Try the examples: python examples.py")
        print("3. Use the CLI: python youtube_transcript_downloader.py --help")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        print("Make sure you have installed all dependencies: pip install -r requirements.txt")
