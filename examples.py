#!/usr/bin/env python3
"""
Example scripts for YouTube Transcript Downloader

This file contains various examples showing how to use the YouTubeTranscriptDownloader
class for different use cases.
"""

from youtube_transcript_downloader import YouTubeTranscriptDownloader
import json


def example_latest_videos():
    """Download transcripts from the latest 10 videos of a channel."""
    print("Example 1: Download latest 10 videos")
    
    downloader = YouTubeTranscriptDownloader(output_dir="examples/latest")
    
    # Replace with actual channel URL
    channel_url = "https://www.youtube.com/@3blue1brown"
    
    criteria = {'latest': 10}
    
    results = downloader.download_transcripts(
        channel_url=channel_url,
        criteria=criteria,
        output_format='txt',
        include_timestamps=True
    )
    
    print(f"✅ Downloaded {results['downloaded']} transcripts")
    print(f"❌ Failed: {results['failed']}")
    return results


def example_popular_videos():
    """Download transcripts from the most popular videos."""
    print("\nExample 2: Download 5 most popular videos")
    
    downloader = YouTubeTranscriptDownloader(output_dir="examples/popular")
    
    channel_url = "https://www.youtube.com/@veritasium"
    
    criteria = {
        'popular': 5,
        'min_views': 100000  # Only videos with at least 100k views
    }
    
    results = downloader.download_transcripts(
        channel_url=channel_url,
        criteria=criteria,
        output_format='json',
        languages=['en']
    )
    
    print(f"✅ Downloaded {results['downloaded']} transcripts")
    return results


def example_date_range():
    """Download transcripts from videos within a specific date range."""
    print("\nExample 3: Download videos from 2023")
    
    downloader = YouTubeTranscriptDownloader(output_dir="examples/date_range")
    
    channel_url = "https://www.youtube.com/@kurzgesagt"
    
    criteria = {
        'date_from': '2023-01-01',
        'date_to': '2023-12-31',
        'min_duration': 300,  # At least 5 minutes
        'max_duration': 1200  # At most 20 minutes
    }
    
    results = downloader.download_transcripts(
        channel_url=channel_url,
        criteria=criteria,
        output_format='csv'
    )
    
    print(f"✅ Downloaded {results['downloaded']} transcripts")
    return results


def example_filtered_content():
    """Download transcripts with multiple filters."""
    print("\nExample 4: Download with multiple filters")
    
    downloader = YouTubeTranscriptDownloader(output_dir="examples/filtered")
    
    channel_url = "https://www.youtube.com/@TED"
    
    criteria = {
        'latest': 50,  # Look at latest 50 videos
        'min_views': 50000,  # At least 50k views
        'min_duration': 600,  # At least 10 minutes
        'max_duration': 1800  # At most 30 minutes
    }
    
    results = downloader.download_transcripts(
        channel_url=channel_url,
        criteria=criteria,
        output_format='txt',
        include_timestamps=True,
        languages=['en', 'es']  # Prefer English, fallback to Spanish
    )
    
    print(f"✅ Downloaded {results['downloaded']} transcripts")
    return results


def example_batch_channels():
    """Download transcripts from multiple channels."""
    print("\nExample 5: Batch download from multiple channels")
    
    channels = [
        "https://www.youtube.com/@3blue1brown",
        "https://www.youtube.com/@veritasium",
        "https://www.youtube.com/@kurzgesagt"
    ]
    
    all_results = []
    
    for i, channel_url in enumerate(channels):
        print(f"\nProcessing channel {i+1}/{len(channels)}: {channel_url}")
        
        # Create separate directory for each channel
        channel_name = channel_url.split('@')[-1]
        downloader = YouTubeTranscriptDownloader(output_dir=f"examples/batch/{channel_name}")
        
        criteria = {'latest': 5}  # Latest 5 videos from each channel
        
        results = downloader.download_transcripts(
            channel_url=channel_url,
            criteria=criteria,
            output_format='json'
        )
        
        all_results.append({
            'channel': channel_url,
            'results': results
        })
        
        print(f"  ✅ Downloaded {results['downloaded']} transcripts")
    
    return all_results


def example_custom_analysis():
    """Example of using the API for custom analysis."""
    print("\nExample 6: Custom analysis workflow")
    
    downloader = YouTubeTranscriptDownloader(output_dir="examples/analysis")
    
    channel_url = "https://www.youtube.com/@crashcourse"
    
    # Step 1: Get all videos from channel
    videos = downloader.get_channel_videos(channel_url, max_videos=100)
    print(f"Found {len(videos)} videos")
    
    # Step 2: Apply custom filtering
    criteria = {
        'min_views': 10000,
        'min_duration': 300
    }
    filtered_videos = downloader.filter_videos_by_criteria(videos, criteria)
    print(f"After filtering: {len(filtered_videos)} videos")
    
    # Step 3: Download transcripts for analysis
    transcript_data = []
    
    for video in filtered_videos[:10]:  # Limit to first 10 for example
        transcript = downloader.get_transcript(video['id'])
        if transcript:
            # Extract just the text for analysis
            text = downloader.format_transcript_text(transcript['transcript'])
            
            transcript_data.append({
                'video_id': video['id'],
                'title': video['title'],
                'view_count': video['view_count'],
                'text_length': len(text),
                'word_count': len(text.split()),
                'transcript_text': text[:500] + "..."  # First 500 chars
            })
    
    # Save analysis results
    with open('examples/analysis/analysis_results.json', 'w') as f:
        json.dump(transcript_data, f, indent=2)
    
    print(f"✅ Analyzed {len(transcript_data)} videos")
    return transcript_data


if __name__ == '__main__':
    print("YouTube Transcript Downloader - Examples")
    print("=" * 50)
    
    # Run examples (comment out the ones you don't want to run)
    
    # example_latest_videos()
    # example_popular_videos()
    # example_date_range()
    # example_filtered_content()
    # example_batch_channels()
    # example_custom_analysis()
    
    print("\n" + "=" * 50)
    print("Examples completed! Check the 'examples/' directory for output files.")
    print("\nTo run individual examples, uncomment the function calls above.")
