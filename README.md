# YouTube Transcript Downloader

A comprehensive Python tool to download transcripts from YouTube channels with various filtering criteria. This tool supports downloading transcripts from specific videos, latest videos, popular videos, and videos within date ranges.

## Features

- 🎯 **Multiple Download Criteria**: Latest N videos, most popular videos, all videos, date ranges
- 🌍 **Multi-language Support**: Download transcripts in preferred languages
- 📄 **Multiple Output Formats**: TXT, JSON, CSV
- ⏰ **Timestamp Support**: Include timestamps in transcript output
- 🔍 **Advanced Filtering**: Filter by view count, duration, upload date
- 📊 **Progress Tracking**: Real-time progress bars and detailed logging
- 🛡️ **Error Handling**: Robust handling of missing transcripts and private videos

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### Command Line Usage

```bash
# Download latest 10 videos from a channel
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --latest 10

# Download most popular 5 videos
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --popular 5

# Download all videos (be careful with large channels!)
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --all

# Download with specific criteria
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" \
    --min-views 10000 --max-views 100000 --format json --timestamps
```

### Python API Usage

```python
from youtube_transcript_downloader import YouTubeTranscriptDownloader

# Initialize downloader
downloader = YouTubeTranscriptDownloader(output_dir="my_transcripts")

# Download latest 20 videos
criteria = {'latest': 20}
results = downloader.download_transcripts(
    channel_url="https://www.youtube.com/@channelname",
    criteria=criteria,
    output_format='txt',
    include_timestamps=True
)

print(f"Downloaded {results['downloaded']} transcripts")
```

## Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `--latest N` | Download latest N videos | `--latest 10` |
| `--popular N` | Download most popular N videos | `--popular 5` |
| `--all` | Download all videos from channel | `--all` |
| `--min-views N` | Minimum view count filter | `--min-views 1000` |
| `--max-views N` | Maximum view count filter | `--max-views 100000` |
| `--min-duration N` | Minimum duration in seconds | `--min-duration 300` |
| `--max-duration N` | Maximum duration in seconds | `--max-duration 3600` |
| `--date-from DATE` | Start date (YYYY-MM-DD) | `--date-from 2023-01-01` |
| `--date-to DATE` | End date (YYYY-MM-DD) | `--date-to 2023-12-31` |
| `--format FORMAT` | Output format (txt/json/csv) | `--format json` |
| `--languages LANGS` | Preferred languages | `--languages en,es,fr` |
| `--timestamps` | Include timestamps in output | `--timestamps` |
| `--output-dir DIR` | Output directory | `--output-dir ./transcripts` |

## Examples

### 1. Educational Channel Analysis
```bash
# Download transcripts from an educational channel for the last year
python youtube_transcript_downloader.py "https://www.youtube.com/@3blue1brown" \
    --date-from 2023-01-01 --date-to 2023-12-31 \
    --format json --timestamps
```

### 2. Popular Content Research
```bash
# Get transcripts from the 20 most popular videos
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" \
    --popular 20 --min-views 50000 --format csv
```

### 3. Recent Content Monitoring
```bash
# Download latest 50 videos with timestamps
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" \
    --latest 50 --timestamps --format txt
```

### 4. Filtered Content Download
```bash
# Download videos between 5-30 minutes with good engagement
python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" \
    --min-duration 300 --max-duration 1800 \
    --min-views 10000 --format json
```

## Output Formats

### TXT Format
- Human-readable text with video metadata
- Optional timestamps
- Clean, formatted transcript text

### JSON Format
- Complete video metadata
- Structured transcript data with timing
- Machine-readable format for analysis

### CSV Format
- Spreadsheet-compatible format
- Columns: start_time, duration, text
- Easy to import into data analysis tools

## Error Handling

The tool gracefully handles common issues:
- Videos without transcripts
- Private or deleted videos
- Network connectivity issues
- Rate limiting
- Invalid channel URLs

## Limitations

- Respects YouTube's terms of service
- Only downloads publicly available transcripts
- Some videos may not have transcripts available
- Rate limiting may slow down large downloads

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is for educational and research purposes. Please respect YouTube's terms of service and content creators' rights.
