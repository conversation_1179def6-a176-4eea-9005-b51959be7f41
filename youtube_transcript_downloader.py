#!/usr/bin/env python3
"""
YouTube Transcript Downloader

A comprehensive tool to download transcripts from YouTube channels with various filtering criteria.
Supports downloading transcripts from:
- Last N videos
- All videos from a channel
- Popular videos
- Videos within date ranges
- Specific video lists

Author: AI Assistant
"""

import os
import json
import csv
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union
from pathlib import Path

import yt_dlp
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound
import pandas as pd
from tqdm import tqdm
import click
from dateutil import parser as date_parser


class YouTubeTranscriptDownloader:
    """Main class for downloading YouTube transcripts with various filtering options."""
    
    def __init__(self, output_dir: str = "transcripts", log_level: str = "INFO"):
        """
        Initialize the transcript downloader.
        
        Args:
            output_dir: Directory to save transcripts
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # yt-dlp configuration
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'ignoreerrors': True,
        }
    
    def get_channel_videos(self, channel_url: str, max_videos: Optional[int] = None) -> List[Dict]:
        """
        Get video information from a YouTube channel.
        
        Args:
            channel_url: YouTube channel URL or handle
            max_videos: Maximum number of videos to retrieve (None for all)
            
        Returns:
            List of video dictionaries with metadata
        """
        self.logger.info(f"Fetching videos from channel: {channel_url}")
        
        ydl_opts = self.ydl_opts.copy()
        if max_videos:
            ydl_opts['playlistend'] = max_videos
            
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Extract channel info
                channel_info = ydl.extract_info(channel_url, download=False)
                
                if 'entries' not in channel_info:
                    self.logger.error("No videos found in channel")
                    return []
                
                videos = []
                for entry in channel_info['entries']:
                    if entry:  # Skip None entries
                        videos.append({
                            'id': entry.get('id'),
                            'title': entry.get('title'),
                            'url': f"https://www.youtube.com/watch?v={entry.get('id')}",
                            'duration': entry.get('duration'),
                            'upload_date': entry.get('upload_date'),
                            'view_count': entry.get('view_count'),
                            'like_count': entry.get('like_count'),
                        })
                
                self.logger.info(f"Found {len(videos)} videos")
                return videos
                
        except Exception as e:
            self.logger.error(f"Error fetching channel videos: {str(e)}")
            return []
    
    def get_video_details(self, video_ids: List[str]) -> List[Dict]:
        """
        Get detailed information for specific videos.
        
        Args:
            video_ids: List of YouTube video IDs
            
        Returns:
            List of detailed video dictionaries
        """
        videos = []
        ydl_opts = self.ydl_opts.copy()
        ydl_opts['extract_flat'] = False
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            for video_id in tqdm(video_ids, desc="Fetching video details"):
                try:
                    url = f"https://www.youtube.com/watch?v={video_id}"
                    info = ydl.extract_info(url, download=False)
                    
                    videos.append({
                        'id': info.get('id'),
                        'title': info.get('title'),
                        'url': url,
                        'duration': info.get('duration'),
                        'upload_date': info.get('upload_date'),
                        'view_count': info.get('view_count'),
                        'like_count': info.get('like_count'),
                        'description': info.get('description'),
                        'uploader': info.get('uploader'),
                        'upload_timestamp': info.get('timestamp'),
                    })
                except Exception as e:
                    self.logger.warning(f"Error fetching details for video {video_id}: {str(e)}")
                    
        return videos

    def filter_videos_by_criteria(self, videos: List[Dict], criteria: Dict) -> List[Dict]:
        """
        Filter videos based on various criteria.

        Args:
            videos: List of video dictionaries
            criteria: Dictionary with filtering criteria
                - latest: int - Get latest N videos
                - popular: int - Get most popular N videos (by view count)
                - min_views: int - Minimum view count
                - max_views: int - Maximum view count
                - date_from: str - Start date (YYYY-MM-DD)
                - date_to: str - End date (YYYY-MM-DD)
                - min_duration: int - Minimum duration in seconds
                - max_duration: int - Maximum duration in seconds

        Returns:
            Filtered list of videos
        """
        filtered_videos = videos.copy()

        # Filter by view count range
        if criteria.get('min_views'):
            filtered_videos = [v for v in filtered_videos
                             if v.get('view_count', 0) >= criteria['min_views']]

        if criteria.get('max_views'):
            filtered_videos = [v for v in filtered_videos
                             if v.get('view_count', float('inf')) <= criteria['max_views']]

        # Filter by duration
        if criteria.get('min_duration'):
            filtered_videos = [v for v in filtered_videos
                             if v.get('duration', 0) >= criteria['min_duration']]

        if criteria.get('max_duration'):
            filtered_videos = [v for v in filtered_videos
                             if v.get('duration', float('inf')) <= criteria['max_duration']]

        # Filter by date range
        if criteria.get('date_from') or criteria.get('date_to'):
            date_filtered = []
            for video in filtered_videos:
                upload_date = video.get('upload_date')
                if upload_date:
                    try:
                        video_date = datetime.strptime(upload_date, '%Y%m%d')

                        if criteria.get('date_from'):
                            from_date = date_parser.parse(criteria['date_from'])
                            if video_date < from_date:
                                continue

                        if criteria.get('date_to'):
                            to_date = date_parser.parse(criteria['date_to'])
                            if video_date > to_date:
                                continue

                        date_filtered.append(video)
                    except ValueError:
                        self.logger.warning(f"Invalid date format for video: {video.get('title')}")

            filtered_videos = date_filtered

        # Sort and limit by criteria
        if criteria.get('popular'):
            # Sort by view count (descending) and take top N
            filtered_videos.sort(key=lambda x: x.get('view_count', 0), reverse=True)
            filtered_videos = filtered_videos[:criteria['popular']]

        elif criteria.get('latest'):
            # Sort by upload date (descending) and take latest N
            filtered_videos.sort(key=lambda x: x.get('upload_date', ''), reverse=True)
            filtered_videos = filtered_videos[:criteria['latest']]

        self.logger.info(f"Filtered to {len(filtered_videos)} videos based on criteria")
        return filtered_videos

    def get_transcript(self, video_id: str, languages: List[str] = ['en']) -> Optional[Dict]:
        """
        Get transcript for a specific video.

        Args:
            video_id: YouTube video ID
            languages: List of preferred languages (default: ['en'])

        Returns:
            Dictionary with transcript data or None if not available
        """
        try:
            # Try to get transcript in preferred languages
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)

            transcript = None
            for lang in languages:
                try:
                    transcript = transcript_list.find_transcript([lang])
                    break
                except NoTranscriptFound:
                    continue

            # If no transcript in preferred languages, try auto-generated English
            if not transcript:
                try:
                    transcript = transcript_list.find_generated_transcript(['en'])
                except NoTranscriptFound:
                    # Try any available transcript
                    try:
                        available_transcripts = list(transcript_list)
                        if available_transcripts:
                            transcript = available_transcripts[0]
                    except:
                        return None

            if transcript:
                transcript_data = transcript.fetch()
                return {
                    'language': transcript.language,
                    'language_code': transcript.language_code,
                    'is_generated': transcript.is_generated,
                    'transcript': transcript_data
                }

        except (TranscriptsDisabled, NoTranscriptFound, Exception) as e:
            self.logger.debug(f"No transcript available for video {video_id}: {str(e)}")
            return None

        return None

    def format_transcript_text(self, transcript_data: List[Dict]) -> str:
        """
        Format transcript data into readable text.

        Args:
            transcript_data: List of transcript segments

        Returns:
            Formatted transcript text
        """
        return ' '.join([segment['text'] for segment in transcript_data])

    def format_transcript_with_timestamps(self, transcript_data: List[Dict]) -> str:
        """
        Format transcript data with timestamps.

        Args:
            transcript_data: List of transcript segments

        Returns:
            Formatted transcript text with timestamps
        """
        formatted_lines = []
        for segment in transcript_data:
            start_time = segment['start']
            minutes = int(start_time // 60)
            seconds = int(start_time % 60)
            timestamp = f"[{minutes:02d}:{seconds:02d}]"
            formatted_lines.append(f"{timestamp} {segment['text']}")

        return '\n'.join(formatted_lines)

    def save_transcript(self, video_info: Dict, transcript_data: Dict,
                       format_type: str = 'txt', include_timestamps: bool = False) -> str:
        """
        Save transcript to file.

        Args:
            video_info: Video metadata dictionary
            transcript_data: Transcript data dictionary
            format_type: Output format ('txt', 'json', 'csv')
            include_timestamps: Whether to include timestamps in text format

        Returns:
            Path to saved file
        """
        # Create safe filename
        safe_title = "".join(c for c in video_info['title'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title[:100]  # Limit length

        video_id = video_info['id']
        filename = f"{safe_title}_{video_id}.{format_type}"
        filepath = self.output_dir / filename

        if format_type == 'txt':
            if include_timestamps:
                content = self.format_transcript_with_timestamps(transcript_data['transcript'])
            else:
                content = self.format_transcript_text(transcript_data['transcript'])

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Title: {video_info['title']}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"URL: {video_info['url']}\n")
                f.write(f"Language: {transcript_data['language']} ({transcript_data['language_code']})\n")
                f.write(f"Auto-generated: {transcript_data['is_generated']}\n")
                f.write(f"Upload Date: {video_info.get('upload_date', 'Unknown')}\n")
                f.write("-" * 50 + "\n\n")
                f.write(content)

        elif format_type == 'json':
            data = {
                'video_info': video_info,
                'transcript_info': {
                    'language': transcript_data['language'],
                    'language_code': transcript_data['language_code'],
                    'is_generated': transcript_data['is_generated']
                },
                'transcript': transcript_data['transcript']
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        elif format_type == 'csv':
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['start_time', 'duration', 'text'])

                for segment in transcript_data['transcript']:
                    writer.writerow([
                        segment['start'],
                        segment['duration'],
                        segment['text']
                    ])

        self.logger.info(f"Saved transcript to: {filepath}")
        return str(filepath)

    def download_transcripts(self, channel_url: str, criteria: Dict = None,
                           output_format: str = 'txt', include_timestamps: bool = False,
                           languages: List[str] = ['en']) -> Dict:
        """
        Download transcripts from a YouTube channel based on criteria.

        Args:
            channel_url: YouTube channel URL
            criteria: Filtering criteria dictionary
            output_format: Output format ('txt', 'json', 'csv')
            include_timestamps: Whether to include timestamps
            languages: Preferred transcript languages

        Returns:
            Dictionary with download results
        """
        if criteria is None:
            criteria = {}

        self.logger.info(f"Starting transcript download for channel: {channel_url}")

        # Get videos from channel
        max_videos = criteria.get('latest') or criteria.get('popular') or None
        videos = self.get_channel_videos(channel_url, max_videos)

        if not videos:
            return {'success': False, 'error': 'No videos found in channel'}

        # Apply filtering criteria
        if criteria:
            videos = self.filter_videos_by_criteria(videos, criteria)

        if not videos:
            return {'success': False, 'error': 'No videos match the specified criteria'}

        # Download transcripts
        results = {
            'success': True,
            'total_videos': len(videos),
            'downloaded': 0,
            'failed': 0,
            'files': [],
            'errors': []
        }

        for video in tqdm(videos, desc="Downloading transcripts"):
            try:
                transcript_data = self.get_transcript(video['id'], languages)

                if transcript_data:
                    filepath = self.save_transcript(
                        video, transcript_data, output_format, include_timestamps
                    )
                    results['files'].append(filepath)
                    results['downloaded'] += 1
                else:
                    error_msg = f"No transcript available for: {video['title']}"
                    self.logger.warning(error_msg)
                    results['errors'].append(error_msg)
                    results['failed'] += 1

            except Exception as e:
                error_msg = f"Error processing {video['title']}: {str(e)}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
                results['failed'] += 1

        self.logger.info(f"Download complete. Success: {results['downloaded']}, Failed: {results['failed']}")
        return results


# CLI Interface
@click.command()
@click.argument('channel_url')
@click.option('--output-dir', '-o', default='transcripts', help='Output directory for transcripts')
@click.option('--format', '-f', 'output_format', default='txt',
              type=click.Choice(['txt', 'json', 'csv']), help='Output format')
@click.option('--latest', '-l', type=int, help='Download latest N videos')
@click.option('--popular', '-p', type=int, help='Download most popular N videos')
@click.option('--all', 'download_all', is_flag=True, help='Download all videos')
@click.option('--min-views', type=int, help='Minimum view count')
@click.option('--max-views', type=int, help='Maximum view count')
@click.option('--min-duration', type=int, help='Minimum duration in seconds')
@click.option('--max-duration', type=int, help='Maximum duration in seconds')
@click.option('--date-from', help='Start date (YYYY-MM-DD)')
@click.option('--date-to', help='End date (YYYY-MM-DD)')
@click.option('--languages', '-lang', default='en', help='Preferred languages (comma-separated)')
@click.option('--timestamps', is_flag=True, help='Include timestamps in text format')
@click.option('--log-level', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']), help='Logging level')
def main(channel_url, output_dir, output_format, latest, popular, download_all,
         min_views, max_views, min_duration, max_duration, date_from, date_to,
         languages, timestamps, log_level):
    """
    Download YouTube transcripts from a channel with various filtering options.

    CHANNEL_URL: YouTube channel URL (e.g., https://www.youtube.com/@channelname)

    Examples:

    # Download latest 10 videos
    python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --latest 10

    # Download most popular 5 videos
    python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --popular 5

    # Download videos with specific view count range
    python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --min-views 10000 --max-views 100000

    # Download videos from date range
    python youtube_transcript_downloader.py "https://www.youtube.com/@channelname" --date-from 2023-01-01 --date-to 2023-12-31
    """

    # Parse languages
    lang_list = [lang.strip() for lang in languages.split(',')]

    # Build criteria dictionary
    criteria = {}
    if latest:
        criteria['latest'] = latest
    elif popular:
        criteria['popular'] = popular

    if min_views:
        criteria['min_views'] = min_views
    if max_views:
        criteria['max_views'] = max_views
    if min_duration:
        criteria['min_duration'] = min_duration
    if max_duration:
        criteria['max_duration'] = max_duration
    if date_from:
        criteria['date_from'] = date_from
    if date_to:
        criteria['date_to'] = date_to

    # Initialize downloader
    downloader = YouTubeTranscriptDownloader(output_dir, log_level)

    try:
        # Download transcripts
        results = downloader.download_transcripts(
            channel_url=channel_url,
            criteria=criteria if criteria else None,
            output_format=output_format,
            include_timestamps=timestamps,
            languages=lang_list
        )

        # Print results
        if results['success']:
            click.echo(f"\n✅ Download completed successfully!")
            click.echo(f"📊 Total videos processed: {results['total_videos']}")
            click.echo(f"✅ Successfully downloaded: {results['downloaded']}")
            click.echo(f"❌ Failed: {results['failed']}")
            click.echo(f"📁 Output directory: {output_dir}")

            if results['files']:
                click.echo(f"\n📄 Downloaded files:")
                for file_path in results['files'][:10]:  # Show first 10 files
                    click.echo(f"  - {file_path}")
                if len(results['files']) > 10:
                    click.echo(f"  ... and {len(results['files']) - 10} more files")

            if results['errors']:
                click.echo(f"\n⚠️  Errors encountered:")
                for error in results['errors'][:5]:  # Show first 5 errors
                    click.echo(f"  - {error}")
                if len(results['errors']) > 5:
                    click.echo(f"  ... and {len(results['errors']) - 5} more errors")
        else:
            click.echo(f"❌ Download failed: {results.get('error', 'Unknown error')}")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}")
        raise click.Abort()


if __name__ == '__main__':
    main()
