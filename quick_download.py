#!/usr/bin/env python3
"""
Quick Download Script for YouTube Transcript Downloader

This script provides a simple interactive interface for common download scenarios.
"""

from youtube_transcript_downloader import YouTubeTranscriptDownloader
import os


def get_user_input():
    """Get user input for download parameters."""
    print("YouTube Transcript Downloader - Quick Download")
    print("=" * 50)
    
    # Get channel URL
    while True:
        channel_url = input("\nEnter YouTube channel URL (e.g., https://www.youtube.com/@channelname): ").strip()
        if channel_url and ("youtube.com" in channel_url or "youtu.be" in channel_url):
            break
        print("❌ Please enter a valid YouTube channel URL")
    
    # Get download type
    print("\nSelect download type:")
    print("1. Latest N videos")
    print("2. Most popular N videos")
    print("3. All videos (be careful with large channels!)")
    print("4. Videos from date range")
    print("5. Custom criteria")
    
    while True:
        choice = input("\nEnter your choice (1-5): ").strip()
        if choice in ['1', '2', '3', '4', '5']:
            break
        print("❌ Please enter a number between 1 and 5")
    
    criteria = {}
    
    if choice == '1':
        # Latest N videos
        while True:
            try:
                n = int(input("How many latest videos? (e.g., 10): "))
                if n > 0:
                    criteria['latest'] = n
                    break
                print("❌ Please enter a positive number")
            except ValueError:
                print("❌ Please enter a valid number")
    
    elif choice == '2':
        # Most popular N videos
        while True:
            try:
                n = int(input("How many popular videos? (e.g., 5): "))
                if n > 0:
                    criteria['popular'] = n
                    break
                print("❌ Please enter a positive number")
            except ValueError:
                print("❌ Please enter a valid number")
    
    elif choice == '3':
        # All videos
        confirm = input("⚠️  This will download ALL videos from the channel. Continue? (y/N): ")
        if confirm.lower() != 'y':
            print("Operation cancelled.")
            return None
        # No criteria needed for all videos
    
    elif choice == '4':
        # Date range
        print("Enter date range (YYYY-MM-DD format):")
        date_from = input("From date (e.g., 2023-01-01): ").strip()
        date_to = input("To date (e.g., 2023-12-31): ").strip()
        
        if date_from:
            criteria['date_from'] = date_from
        if date_to:
            criteria['date_to'] = date_to
    
    elif choice == '5':
        # Custom criteria
        print("\nCustom criteria (press Enter to skip any option):")
        
        latest = input("Latest N videos: ").strip()
        if latest:
            try:
                criteria['latest'] = int(latest)
            except ValueError:
                pass
        
        min_views = input("Minimum views: ").strip()
        if min_views:
            try:
                criteria['min_views'] = int(min_views)
            except ValueError:
                pass
        
        max_views = input("Maximum views: ").strip()
        if max_views:
            try:
                criteria['max_views'] = int(max_views)
            except ValueError:
                pass
        
        min_duration = input("Minimum duration (seconds): ").strip()
        if min_duration:
            try:
                criteria['min_duration'] = int(min_duration)
            except ValueError:
                pass
    
    # Get output format
    print("\nSelect output format:")
    print("1. TXT (human-readable)")
    print("2. JSON (structured data)")
    print("3. CSV (spreadsheet-compatible)")
    
    while True:
        fmt_choice = input("Enter format choice (1-3): ").strip()
        if fmt_choice == '1':
            output_format = 'txt'
            break
        elif fmt_choice == '2':
            output_format = 'json'
            break
        elif fmt_choice == '3':
            output_format = 'csv'
            break
        print("❌ Please enter 1, 2, or 3")
    
    # Get timestamp preference for TXT format
    include_timestamps = False
    if output_format == 'txt':
        timestamps = input("Include timestamps? (y/N): ").strip().lower()
        include_timestamps = timestamps == 'y'
    
    # Get output directory
    output_dir = input("Output directory (press Enter for 'transcripts'): ").strip()
    if not output_dir:
        output_dir = 'transcripts'
    
    return {
        'channel_url': channel_url,
        'criteria': criteria if criteria else None,
        'output_format': output_format,
        'include_timestamps': include_timestamps,
        'output_dir': output_dir
    }


def main():
    """Main function for quick download."""
    try:
        # Get user input
        params = get_user_input()
        if not params:
            return
        
        print(f"\n{'='*50}")
        print("Starting download...")
        print(f"Channel: {params['channel_url']}")
        print(f"Criteria: {params['criteria'] or 'All videos'}")
        print(f"Format: {params['output_format'].upper()}")
        print(f"Output: {params['output_dir']}")
        print(f"{'='*50}")
        
        # Initialize downloader
        downloader = YouTubeTranscriptDownloader(
            output_dir=params['output_dir'],
            log_level="INFO"
        )
        
        # Download transcripts
        results = downloader.download_transcripts(
            channel_url=params['channel_url'],
            criteria=params['criteria'],
            output_format=params['output_format'],
            include_timestamps=params['include_timestamps']
        )
        
        # Display results
        print(f"\n{'='*50}")
        print("DOWNLOAD COMPLETE!")
        print(f"{'='*50}")
        
        if results['success']:
            print(f"✅ Successfully downloaded: {results['downloaded']} transcripts")
            print(f"❌ Failed: {results['failed']}")
            print(f"📁 Output directory: {params['output_dir']}")
            
            if results['files']:
                print(f"\n📄 Files created:")
                for file_path in results['files'][:10]:  # Show first 10
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    print(f"  - {os.path.basename(file_path)} ({file_size:,} bytes)")
                
                if len(results['files']) > 10:
                    print(f"  ... and {len(results['files']) - 10} more files")
            
            if results['errors']:
                print(f"\n⚠️  Some errors occurred:")
                for error in results['errors'][:3]:  # Show first 3 errors
                    print(f"  - {error}")
                if len(results['errors']) > 3:
                    print(f"  ... and {len(results['errors']) - 3} more errors")
        else:
            print(f"❌ Download failed: {results.get('error', 'Unknown error')}")
        
        print(f"\n🎉 Done! Check the '{params['output_dir']}' directory for your transcripts.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Download cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        print("Please check your internet connection and try again.")


if __name__ == '__main__':
    main()
