#!/usr/bin/env python3
"""
Setup script for YouTube Transcript Downloader
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="youtube-transcript-downloader",
    version="1.0.0",
    author="AI Assistant",
    description="A comprehensive tool to download YouTube transcripts with various filtering criteria",
    long_description=long_description,
    long_description_content_type="text/markdown",
    py_modules=["youtube_transcript_downloader"],
    install_requires=requirements,
    python_requires=">=3.7",
    entry_points={
        "console_scripts": [
            "youtube-transcripts=youtube_transcript_downloader:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Researchers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP",
        "Topic :: Multimedia :: Video",
        "Topic :: Text Processing",
    ],
    keywords="youtube, transcripts, download, video, subtitles, captions",
)
